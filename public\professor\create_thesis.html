<!doctype html>
<html lang="el">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="/stylesheets/style.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
    <title>Create new Thesis</title>
</head>

<body id="body" class="d-flex flex-column min-vh-100">
    <div id="navbar"></div>

    <main class="flex-grow-1">
        <h2 class="display-4 text-center mt-5 p-5"> Διαχείριση Θεμάτων Διπλωματικής</h2>

        <div class="container">

            <button id="new-thesis-button" class="btn btn-primary my-3" data-bs-toggle="modal"
                data-bs-target="#thesisModal">
                Νέο Θέμα
            </button>

            <h4>Υπάρχοντα Θέματα</h4>
            <ul id="table" class="list-group mb-4">
            </ul>
        </div>

        <!-- Modal for creating / updating Thesis -->
        <div class="modal fade" id="thesisModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="#" id="form">
                        <input id="thesisID"  name="id" class="d-none">
                        <div class="modal-header">
                            <h5 class="modal-title" id="createThesisModalLabel">Καταχώρηση Νέου Θέματος</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="title" class="form-label">Τίτλος Θέματος</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label for="summary" class="form-label">Σύνοψη</label>
                                <textarea class="form-control" id="summary" name="summary" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="pdf" class="form-label">Αρχείο PDF</label>
                                <input type="file" class="form-control" id="pdf" name="pdf" accept="application/pdf">
                                <a id="pdfDownload" href="#" target="_blank" class="d-none">View current PDF</a>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Άκυρο</button>
                            <button id="publishBtn" type="submit" class="btn btn-primary">Δημοσίευση</button>
                            <button id="updateBtn" type="submit" class="btn btn-primary d-none">Ενημέρωση</button>
                        </div>
                        <div class="mb-3 me-3">
                            <p id="duplicate-message" class="text-end text-danger"></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <div id="footer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
        </script>
    <script src="/js/loadPartials.js"></script>
    <script src="/js/populateThesisList.js"></script>
</body>

</html>