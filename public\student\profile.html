<!doctype html>
<html lang="el">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="/stylesheets/style.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous" />
    <title>Customize Profile</title>
</head>

<body id="body" class="d-flex flex-column min-vh-100">
    <div id="navbar"></div>

    <main class="flex-grow-1">
        <h2 class="display-4 text-center mt-5 p-5"> Επεξεργασία Προφίλ</h2>

        <div class="col m-3">
            <div class="container-fluid justify-content-center align-items-center h-100">
                <form id="informationForm" class="row mx-auto" style="max-width: 800px; ">
                    <div class="mb-3 col-sm-6">
                        <label for="contactEmail" class="form-label">Email επικοινωνίας</label>
                        <input type="email" class="form-control" id="contactEmail" aria-describedby="emailHelp"
                            value="<EMAIL>">
                        <div id="emailHelp" class="form-text">Αυτό το email θα χρησιμοποιηθεί για επίσημη επικοινωνία.
                        </div>
                    </div>

                    <div class="mb-3 col-sm-6">
                        <label for="mobilePhone" class="form-label">Κινητό τηλέφωνο</label>
                        <input type="tel" class="form-control" id="mobilePhone" value="+3069XXXXXXXX">
                        <div class="form-text">Παρακαλούμε εισάγετε τον αριθμό κινητού τηλεφώνου σας.</div>
                    </div>

                    <div class="mb-3 col-sm-6">
                        <label for="landlinePhone" class="form-label">Σταθερό τηλέφωνο</label>
                        <input type="tel" class="form-control" id="landlinePhone" value="+302610XXXXXX">
                        <div class="form-text">Εισάγετε τον αριθμό σταθερού τηλεφώνου σας (προαιρετικό).</div>
                    </div>

                    <div class="mb-3 col-sm-4">
                        <label for="addressStreet" class="form-label">Οδός</label>
                        <input type="text" class="form-control" id="addressStreet" value="Odos Akadimias">
                    </div>
                    <div class="mb-3 col-sm-2">
                        <label for="streetNumber" class="form-label">Αριθμος</label>
                        <input type="text" class="form-control" id="streetNumber" value="10">
                    </div>
                    <div class="mb-3 col-sm-6">
                        <label for="addressCity" class="form-label">Πόλη</label>
                        <input type="text" class="form-control" id="addressCity" value="Patra">
                    </div>
                    <div class="mb-3 col-sm-6">
                        <label for="addressZip" class="form-label">Τ.Κ. (Ταχυδρομικός Κώδικας)</label>
                        <input type="text" class="form-control" id="addressZip" value="26504">
                    </div>
                    <hr>
                    <div class="mb-5">
                        <label for="password" class="form-label">Κωδικός (για επιβεβαίωση αλλαγών)</label>
                        <input type="password" class="form-control" id="password">
                    </div>
                    <button id="submitButton" type="submit" class="btn btn-primary disabled">Αποθήκευση Αλλαγών</button>
                    <p id="statusText" class="mt-3 mb-1 d-none"><em>This line rendered as italicized text.</em></p>

                </form>
            </div>
        </div>

    </main>

    <div id="footer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
        </script>
    <script src="/js/loadPartials.js"></script>
    <script src="/js/customizeProfile.js"></script>
</body>

</html>