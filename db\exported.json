{"code": 1, "message": "Retrieved successfully", "students": [{"id": "1", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10433999", "street": "test street", "number": "45", "city": "test city", "postcode": "39955", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2610333000", "mobile_telephone": "6939096979", "email": "<EMAIL>"}, {"id": "5", "name": "<PERSON>", "surname": "<PERSON>", "student_number": "10434000", "street": "<PERSON><PERSON><PERSON>", "number": "18", "city": "Athens", "postcode": "10431", "father_name": "<PERSON>", "landline_telephone": "2610123456", "mobile_telephone": "6970001112", "email": "<EMAIL>"}, {"id": "6", "name": "<PERSON><PERSON>", "surname": "Verikokos", "student_number": "10434001", "street": "<PERSON><PERSON>", "number": "20", "city": "Thessaloniki", "postcode": "54248", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2610778899", "mobile_telephone": "6970001112", "email": "<EMAIL>"}, {"id": "7", "name": "test", "surname": "name", "student_number": "10434002", "street": "str", "number": "1", "city": "patra", "postcode": "26222", "father_name": "father", "landline_telephone": "2610123456", "mobile_telephone": "6912345678", "email": "<EMAIL>"}, {"id": "8", "name": "<PERSON>", "surname": "<PERSON>", "student_number": "10434003", "street": "Fascination", "number": "17", "city": "London", "postcode": "1989", "father_name": "<PERSON>", "landline_telephone": "2610251989", "mobile_telephone": "6902051989", "email": "<EMAIL>"}, {"id": "9", "name": "<PERSON>", "surname": "Tyrannosaurus", "student_number": "10434004", "street": "Cretaceous", "number": "2", "city": "Larami<PERSON>", "postcode": "54321", "father_name": "Daspletosaurus", "landline_telephone": "2610432121", "mobile_telephone": "6911231234", "email": "<EMAIL>"}, {"id": "10", "name": "<PERSON>", "surname": "Mescal ", "student_number": "10434005", "street": "Smith Str.", "number": "33", "city": "New York ", "postcode": "59", "father_name": "<PERSON>", "landline_telephone": "-", "mobile_telephone": "-", "email": "<EMAIL>"}, {"id": "11", "name": "<PERSON>", "surname": "<PERSON>", "student_number": "10434006", "street": "<PERSON>", "number": "90", "city": "New York ", "postcode": "70", "father_name": "<PERSON> ", "landline_telephone": "-", "mobile_telephone": "-", "email": "<EMAIL>"}, {"id": "12", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434007", "street": "<PERSON><PERSON><PERSON>", "number": "29", "city": "New York", "postcode": "26", "father_name": "<PERSON>", "landline_telephone": "-", "mobile_telephone": "-", "email": "<EMAIL>"}, {"id": "13", "name": "<PERSON>", "surname": "Del Rey ", "student_number": "10434008", "street": "Groove Str.", "number": "23", "city": "Los Angeles", "postcode": "1", "father_name": "none", "landline_telephone": "-", "mobile_telephone": "-", "email": "<EMAIL>"}, {"id": "14", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434009", "street": "Magic Str. ", "number": "8", "city": "New Orleans", "postcode": "35", "father_name": "<PERSON> ", "landline_telephone": "56", "mobile_telephone": "67", "email": "<EMAIL>"}, {"id": "15", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434010", "street": "Substance Str.", "number": "25", "city": "Los Angeles ", "postcode": "7", "father_name": "<PERSON>", "landline_telephone": "67", "mobile_telephone": "90", "email": "<EMAIL>"}, {"id": "16", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434011", "street": "Pearl Str. ", "number": "4", "city": "Michigan", "postcode": "8", "father_name": "<PERSON>", "landline_telephone": "-", "mobile_telephone": "-", "email": "<EMAIL>"}, {"id": "17", "name": "Florence ", "surname": "<PERSON><PERSON>", "student_number": "10434012", "street": "Midsommar Str. l", "number": "1", "city": "Away", "postcode": "24", "father_name": "-", "landline_telephone": "5", "mobile_telephone": "2", "email": "<EMAIL>"}, {"id": "18", "name": "PJ ", "surname": "<PERSON>", "student_number": "10434013", "street": "Lonely Str.", "number": "27", "city": "Bridport", "postcode": "-7", "father_name": "<PERSON>", "landline_telephone": "56", "mobile_telephone": "43", "email": "<EMAIL>"}, {"id": "19", "name": "Penélope", "surname": "<PERSON>", "student_number": "10434014", "street": "Almadovar", "number": "55", "city": "Madrid", "postcode": "23", "father_name": "<PERSON> ", "landline_telephone": "5", "mobile_telephone": "4", "email": "<EMAIL>"}, {"id": "20", "name": "<PERSON>", "surname": "Stone", "student_number": "10434015", "street": "Poor Str.", "number": "3", "city": "Paris ", "postcode": "34", "father_name": "none", "landline_telephone": "2333333", "mobile_telephone": "4455555", "email": "<EMAIL>"}, {"id": "21", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434016", "street": "Mpouat Str.", "number": "23", "city": "Athens", "postcode": "10", "father_name": "<PERSON>", "landline_telephone": "09", "mobile_telephone": "45", "email": "<EMAIL>"}, {"id": "22", "name": "<PERSON><PERSON> ", "surname": "<PERSON><PERSON>", "student_number": "10434017", "street": "Desperado Str. ", "number": "24", "city": "Madrid ", "postcode": "656", "father_name": "Sami", "landline_telephone": "344", "mobile_telephone": "221", "email": "<EMAIL>"}, {"id": "23", "name": "<PERSON> ", "surname": "<PERSON><PERSON>", "student_number": "10434018", "street": "Before Str.", "number": "36", "city": "Paris", "postcode": "567", "father_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "1223", "mobile_telephone": "3455", "email": "<EMAIL>"}, {"id": "24", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434019", "street": "Trypes Str.", "number": "3", "city": "Athens", "postcode": "2354", "father_name": "<PERSON><PERSON>", "landline_telephone": "23", "mobile_telephone": "45", "email": "<EMAIL>"}, {"id": "25", "name": "Eleutheria ", "surname": "Arvanitaki", "student_number": "10434020", "street": "Entexno Str. ", "number": "2", "city": "Athens", "postcode": "345", "father_name": "Kosmos", "landline_telephone": "657", "mobile_telephone": "345", "email": "<EMAIL>"}, {"id": "26", "name": "Marina", "surname": "Spanou", "student_number": "10434021", "street": "Pagkrati Str.", "number": "25", "city": "Athens", "postcode": "2456", "father_name": "Gates", "landline_telephone": "897", "mobile_telephone": "354", "email": "<EMAIL>"}, {"id": "27", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434022", "street": "Mpouat Str.", "number": "24", "city": "Athens", "postcode": "5749", "father_name": "<PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "23557", "mobile_telephone": "32453", "email": "<EMAIL>"}, {"id": "28", "name": "Charlotte", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434023", "street": "Boiler Room St", "number": "365", "city": "New York", "postcode": "360", "father_name": "<PERSON>", "landline_telephone": "2610365365", "mobile_telephone": "693653365", "email": "<EMAIL>"}, {"id": "29", "name": "Rhaenyra", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434024", "street": "Dragon St", "number": "2021", "city": "Kings Landing", "postcode": "2021", "father_name": "Viserys", "landline_telephone": "2610101010", "mobile_telephone": "6910101010", "email": "<EMAIL>"}, {"id": "30", "name": "<PERSON>", "surname": "Dover", "student_number": "10434025", "street": "Colon Str.", "number": "124A", "city": "NY", "postcode": "11045", "father_name": "<PERSON>", "landline_telephone": "2584694587", "mobile_telephone": "5841852384", "email": "<EMAIL>"}, {"id": "31", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434026", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "266", "city": "<PERSON><PERSON>", "postcode": "26223", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "+302105562567", "mobile_telephone": "+306975562567", "email": "<EMAIL>"}, {"id": "32", "name": "<PERSON> ", "surname": "<PERSON><PERSON>", "student_number": "10434027", "street": "Nosferatu Str.", "number": "34", "city": "London", "postcode": "567", "father_name": "<PERSON>", "landline_telephone": "436", "mobile_telephone": "46478", "email": "<EMAIL>"}, {"id": "33", "name": "<PERSON><PERSON>", "surname": "Nam", "student_number": "10434028", "street": "<PERSON><PERSON><PERSON>", "number": "135", "city": "<PERSON><PERSON>", "postcode": "26440", "father_name": "<PERSON><PERSON>", "landline_telephone": "2610443568", "mobile_telephone": "6978756432", "email": "<EMAIL>"}, {"id": "34", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434029", "street": "Kolokotroni", "number": "6", "city": "Athens", "postcode": "34754", "father_name": "<PERSON>", "landline_telephone": "2104593844", "mobile_telephone": "6987655433", "email": "<EMAIL>"}, {"id": "35", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434030", "street": "Triton", "number": "12", "city": "Salamina", "postcode": "12216", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "210553985", "mobile_telephone": "6946901012", "email": "<EMAIL>"}, {"id": "36", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434031", "street": "<PERSON> ", "number": "33", "city": "London", "postcode": "44391", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2109993719", "mobile_telephone": "6923144642", "email": "<EMAIL>"}, {"id": "37", "name": "<PERSON><PERSON> ", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434032", "street": "<PERSON><PERSON><PERSON>", "number": "4", "city": "<PERSON>rt<PERSON>", "postcode": "32095", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2279036758", "mobile_telephone": "6948308576", "email": "<EMAIL>"}, {"id": "38", "name": "<PERSON>", "surname": "Togia", "student_number": "10434033", "street": "Athinon", "number": "4", "city": "Athens", "postcode": "28482", "father_name": "<PERSON><PERSON>", "landline_telephone": "2100393022", "mobile_telephone": "6953782102", "email": "<EMAIL>"}, {"id": "39", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434034", "street": "korinthou", "number": "56", "city": "patras", "postcode": "56892", "father_name": "nikos", "landline_telephone": "2610485796", "mobile_telephone": "6934527125", "email": "<EMAIL>"}, {"id": "40", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "student_number": "10434035", "street": "<PERSON><PERSON><PERSON><PERSON> kai <PERSON> ", "number": "100", "city": "<PERSON><PERSON>", "postcode": "26500", "father_name": "None", "landline_telephone": "2610381393", "mobile_telephone": "6028371830", "email": "<EMAIL>"}, {"id": "41", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434036", "street": "<PERSON><PERSON><PERSON>", "number": "36", "city": "<PERSON><PERSON>", "postcode": "26500", "father_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "2610995999", "mobile_telephone": "6947937524", "email": "<EMAIL>"}, {"id": "42", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434037", "street": "kolokotroni", "number": "12", "city": "<PERSON><PERSON>", "postcode": "26500", "father_name": "<PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "2610978423", "mobile_telephone": "6935729345", "email": "<EMAIL>"}, {"id": "44", "name": "<PERSON>", "surname": "Diesel", "student_number": "10434038", "street": "Alexandras Ave", "number": "12", "city": "Athens", "postcode": "11521", "father_name": "Iman", "landline_telephone": "2101234567", "mobile_telephone": "6912345678", "email": "<EMAIL>"}, {"id": "46", "name": "<PERSON><PERSON>", "surname": "of Madness", "student_number": "10434039", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "number": "69", "city": "<PERSON><PERSON>", "postcode": "26441", "father_name": "Prafit", "landline_telephone": "2610654321", "mobile_telephone": "6969966996", "email": "<EMAIL>"}, {"id": "47", "name": "fort", "surname": "nite", "student_number": "10434040", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "69", "city": "tilted tower", "postcode": "4747", "father_name": "epic games", "landline_telephone": "2610747474", "mobile_telephone": "6988112233", "email": "<EMAIL>"}, {"id": "48", "name": "Zeus", "surname": "Ikosaleptos", "student_number": "10434041", "street": "Novi", "number": "25", "city": "Athens", "postcode": "20033", "father_name": "Kleft", "landline_telephone": "2109090901", "mobile_telephone": "6900008005", "email": "<EMAIL>"}, {"id": "49", "name": "AG", "surname": "<PERSON>", "student_number": "10434042", "street": "Britpop", "number": "7G", "city": "London", "postcode": "2021", "father_name": "PC Music", "landline_telephone": "2121212121", "mobile_telephone": "1212121212", "email": "<EMAIL>"}, {"id": "50", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434043", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "4", "city": "New York", "postcode": "25486", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2108452666", "mobile_telephone": "6980081351", "email": "<EMAIL>"}, {"id": "51", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434044", "street": "<PERSON><PERSON>", "number": "11", "city": "<PERSON><PERSON>", "postcode": "50501", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "222609123", "mobile_telephone": "698452154", "email": "<EMAIL>"}, {"id": "52", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434045", "street": "Wall Street", "number": "69", "city": "Jerusalem", "postcode": "478", "father_name": "<PERSON>", "landline_telephone": "69696969", "mobile_telephone": "696969420", "email": "<EMAIL>"}, {"id": "53", "name": "Xontro ", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434046", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "number": "69", "city": "Colarato", "postcode": "14121", "father_name": "<PERSON>", "landline_telephone": "6913124205", "mobile_telephone": "4747859625", "email": "<EMAIL>"}, {"id": "54", "name": "Μaria", "surname": "<PERSON><PERSON>", "student_number": "10434047", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "21", "city": "Athens", "postcode": "10437", "father_name": "<PERSON><PERSON>", "landline_telephone": "2109278907", "mobile_telephone": "6945533213", "email": "<EMAIL>"}, {"id": "55", "name": "Eleni", "surname": "Fotiou", "student_number": "10434048", "street": "<PERSON><PERSON> ", "number": "65", "city": "Athens", "postcode": "10556", "father_name": "<PERSON><PERSON>", "landline_telephone": "2108745645", "mobile_telephone": "6978989000", "email": "<EMAIL>"}, {"id": "56", "name": "Xara", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434049", "street": "<PERSON><PERSON><PERSON> ", "number": "54", "city": "Athens", "postcode": "10441", "father_name": "<PERSON><PERSON>", "landline_telephone": "2108724324", "mobile_telephone": "6945622222", "email": "<EMAIL>"}, {"id": "57", "name": "<PERSON><PERSON>", "surname": "Panagi<PERSON><PERSON>", "student_number": "10434050", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "32", "city": "Athens", "postcode": "10439", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2107655555", "mobile_telephone": "6941133333", "email": "<EMAIL>"}, {"id": "58", "name": "<PERSON><PERSON>", "surname": "Daidalos", "student_number": "10434051", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "4", "city": "Athens", "postcode": "11364", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2108534566", "mobile_telephone": "6976644333", "email": "<EMAIL>"}, {"id": "59", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434052", "street": "<PERSON><PERSON>", "number": "9", "city": "Athens", "postcode": "11631", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2107644999", "mobile_telephone": "6976565655", "email": "<EMAIL>"}, {"id": "60", "name": "<PERSON><PERSON><PERSON>", "surname": "Doghouse", "student_number": "10434053", "street": "novi lane", "number": "33", "city": "<PERSON><PERSON>", "postcode": "26478", "father_name": "<PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "2610420420", "mobile_telephone": "6999999999", "email": "<EMAIL>"}, {"id": "61", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434054", "street": "<PERSON><PERSON><PERSON>", "number": "24", "city": "Athens", "postcode": "10563", "father_name": "<PERSON><PERSON>", "landline_telephone": "210-5678901", "mobile_telephone": "693-5678901", "email": "<EMAIL>"}, {"id": "62", "name": "<PERSON><PERSON><PERSON>", "surname": "Panagi<PERSON><PERSON>", "student_number": "10434055", "street": "Kyprou", "number": "42", "city": "<PERSON><PERSON>", "postcode": "26441", "father_name": "Kwstas", "landline_telephone": "2610-123456", "mobile_telephone": "698-1234567", "email": "<EMAIL>"}, {"id": "63", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434056", "street": "Kolokotroni", "number": "10", "city": "Larissa", "postcode": "41222", "father_name": "<PERSON><PERSON>", "landline_telephone": "2410-456789", "mobile_telephone": "697-4567890", "email": "<EMAIL>"}, {"id": "64", "name": "Kyriakos", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434057", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "36", "city": "Volos", "postcode": "10654", "father_name": "<PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "210-6789012", "mobile_telephone": "695-6789012", "email": "<EMAIL>"}, {"id": "65", "name": "<PERSON>", "surname": "Kp", "student_number": "10434058", "street": "<PERSON>elo<PERSON><PERSON> ", "number": "52", "city": "patra", "postcode": "28746", "father_name": "george", "landline_telephone": "2610555555", "mobile_telephone": "6932323232", "email": "<EMAIL>"}, {"id": "66", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "student_number": "10434059", "street": "anapafseos", "number": "34", "city": "patra", "postcode": "26503", "father_name": "takis", "landline_telephone": "2691045092", "mobile_telephone": "69090909", "email": "<EMAIL>"}, {"id": "67", "name": "<PERSON><PERSON><PERSON> ", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434060", "street": "<PERSON><PERSON><PERSON>", "number": "34", "city": "<PERSON><PERSON>", "postcode": "29438", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2610254390", "mobile_telephone": "6943126767", "email": "<EMAIL>"}, {"id": "68", "name": "<PERSON><PERSON><PERSON>", "surname": "Znuts", "student_number": "10434061", "street": "Grove", "number": "12", "city": "San Andreas", "postcode": "123456", "father_name": "NULL", "landline_telephone": "123456789", "mobile_telephone": "123456789", "email": "<EMAIL>"}, {"id": "69", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434062", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "number": "123", "city": "<PERSON><PERSON><PERSON>", "postcode": "23456", "father_name": "<PERSON>", "landline_telephone": "2613456089", "mobile_telephone": "6980987654", "email": "<EMAIL>"}, {"id": "70", "name": "Tinker", "surname": "Bell", "student_number": "10434063", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "55", "city": "<PERSON><PERSON>", "postcode": "2345", "father_name": "Mixail", "landline_telephone": "2456034567", "mobile_telephone": "6987543345", "email": "<EMAIL>"}, {"id": "71", "name": "Lilly", "surname": "Bloom", "student_number": "10434064", "street": "<PERSON><PERSON><PERSON>", "number": "45", "city": "<PERSON><PERSON>", "postcode": "26440", "father_name": "Menelaos", "landline_telephone": "2610435988", "mobile_telephone": "6987555433", "email": "<EMAIL>"}, {"id": "72", "name": "GIORGOS", "surname": "MASOURAS", "student_number": "10434065", "street": "AGIOU IOANNNI RENTI", "number": "7", "city": "PEIRAIAS", "postcode": "47200", "father_name": "PETROS", "landline_telephone": "694837204", "mobile_telephone": "210583603", "email": "<EMAIL>"}, {"id": "73", "name": "KENDRICK", "surname": "NUNN", "student_number": "10434066", "street": "OAKA", "number": "25", "city": "ATHENS", "postcode": "666", "father_name": "GIANNAKOPOULOS", "landline_telephone": "6982736199", "mobile_telephone": "6906443321", "email": "<EMAIL>"}, {"id": "74", "name": "<PERSON><PERSON><PERSON>", "surname": "Mode", "student_number": "10434067", "street": "Enjoy The Silence", "number": "1990", "city": "London", "postcode": "1990", "father_name": "<PERSON>", "landline_telephone": "1234567890", "mobile_telephone": "1234567770", "email": "<EMAIL>"}, {"id": "75", "name": "name", "surname": "surname", "student_number": "10434068", "street": "your", "number": "69", "city": "mom", "postcode": "15584", "father_name": "father", "landline_telephone": "222", "mobile_telephone": "2223", "email": "<EMAIL>"}, {"id": "76", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434069", "street": "<PERSON><PERSON><PERSON>", "number": "12", "city": "Giotopoli", "postcode": "69420", "father_name": "<PERSON>", "landline_telephone": "210 9241993", "mobile_telephone": "6978722312", "email": "<EMAIL>"}, {"id": "77", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434070", "street": "M<PERSON><PERSON>", "number": "10", "city": "Kolonia", "postcode": "12345", "father_name": "<PERSON><PERSON><PERSON><PERSON>", "landline_telephone": "2105858858", "mobile_telephone": "6935358553", "email": "<EMAIL>"}, {"id": "78", "name": "gerry", "surname": "banana", "student_number": "10434071", "street": "lootlake", "number": "12", "city": "tilted", "postcode": "26500", "father_name": "johnesy", "landline_telephone": "6947830287", "mobile_telephone": "2610987632", "email": "<EMAIL>"}, {"id": "79", "name": "grek<PERSON>i", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434072", "street": "kok<PERSON><PERSON>", "number": "69", "city": "thessaloniki", "postcode": "20972", "father_name": "mourlo", "landline_telephone": "6947910234", "mobile_telephone": "2610810763", "email": "<EMAIL>"}, {"id": "80", "name": "<PERSON><PERSON>", "surname": "Mon", "student_number": "10434073", "street": "Novi", "number": "55", "city": "<PERSON><PERSON><PERSON>", "postcode": "99999", "father_name": "<PERSON>", "landline_telephone": "2610550406", "mobile_telephone": "6967486832", "email": "<EMAIL>"}, {"id": "81", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434074", "street": "<PERSON><PERSON><PERSON>", "number": "12", "city": "<PERSON><PERSON>", "postcode": "26222", "father_name": "<PERSON><PERSON>", "landline_telephone": "2610456632", "mobile_telephone": "6975849305", "email": "<EMAIL>"}, {"id": "82", "name": "Xaralampos", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "student_number": "10434075", "street": "Konstantinou<PERSON><PERSON>", "number": "32", "city": "Athens", "postcode": "16524", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2109995555", "mobile_telephone": "6912345678", "email": "<EMAIL>"}, {"id": "83", "name": "kyriakos", "surname": "<PERSON><PERSON>a", "student_number": "10434076", "street": "karaiskaki", "number": "23", "city": "patras", "postcode": "23444", "father_name": "lebron", "landline_telephone": "2214567809", "mobile_telephone": "6972861212", "email": "<EMAIL>"}, {"id": "84", "name": "<PERSON><PERSON><PERSON>", "surname": "Diagrafino", "student_number": "10434077", "street": "emp", "number": "69", "city": "empa", "postcode": "5432", "father_name": "kaae", "landline_telephone": "2101312000", "mobile_telephone": "6913121312", "email": "<EMAIL>"}, {"id": "85", "name": "<PERSON>", "surname": "Db", "student_number": "10434078", "street": "Spiti sou", "number": "3", "city": "<PERSON><PERSON>", "postcode": "26441", "father_name": "sql", "landline_telephone": "2610 123456", "mobile_telephone": "6912345678", "email": "<EMAIL>"}, {"id": "87", "name": "Bombardriro", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "student_number": "10434079", "street": "<PERSON>", "number": "69", "city": "Athens", "postcode": "15344", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "26810 12345", "mobile_telephone": "6909876543", "email": "<EMAIL>"}, {"id": "88", "name": "<PERSON><PERSON><PERSON><PERSON> ", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434080", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "4", "city": "<PERSON><PERSON><PERSON>", "postcode": "23861", "father_name": "bale<PERSON><PERSON> lololo", "landline_telephone": "2610729878", "mobile_telephone": "6983615882", "email": "<EMAIL>"}, {"id": "89", "name": "Ntinos", "surname": "<PERSON><PERSON>", "student_number": "10434081", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "1", "city": "patras", "postcode": "26225", "father_name": "<PERSON><PERSON>", "landline_telephone": "2610222222", "mobile_telephone": "6988888888", "email": "<EMAIL>"}, {"id": "90", "name": "Xara", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434082", "street": "Psilalonia", "number": "12", "city": "<PERSON><PERSON>", "postcode": "26225", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "261000000", "mobile_telephone": "6933333333", "email": "<EMAIL>"}, {"id": "91", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434083", "street": "<PERSON><PERSON><PERSON>", "number": "1", "city": "<PERSON><PERSON>", "postcode": "26225", "father_name": "Foivos", "landline_telephone": "2610777777", "mobile_telephone": "6944444444", "email": "<EMAIL>"}, {"id": "92", "name": "Mina", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434084", "street": "patra", "number": "13", "city": "patras", "postcode": "12345", "father_name": "makis", "landline_telephone": "261044444", "mobile_telephone": "699999999", "email": "<EMAIL>"}, {"id": "93", "name": "Sakis", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434085", "street": "<PERSON><PERSON><PERSON>", "number": "45", "city": "Piece", "postcode": "123", "father_name": "Gol", "landline_telephone": "66666666", "mobile_telephone": "66666666", "email": "<EMAIL>"}, {"id": "94", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>", "student_number": "10434086", "street": "\tNERV Boulevard", "number": "\t4", "city": "\tTokyo-3", "postcode": "192", "father_name": "<PERSON><PERSON>", "landline_telephone": "\t0366666666", "mobile_telephone": "\t08012345678", "email": "<EMAIL>"}, {"id": "95", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "student_number": "10434087", "street": "<PERSON><PERSON><PERSON>", "number": "13", "city": "patra", "postcode": "26441", "father_name": "Kostaw", "landline_telephone": "**********", "mobile_telephone": "**********", "email": "<EMAIL>"}, {"id": "96", "name": "<PERSON><PERSON><PERSON>", "surname": "kolokotronhs", "student_number": "10434088", "street": "al<PERSON><PERSON><PERSON>", "number": "69", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "postcode": "24463", "father_name": "<PERSON><PERSON>", "landline_telephone": "26578953", "mobile_telephone": "**********", "email": "<EMAIL>"}, {"id": "97", "name": "Minas ", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434089", "street": "<PERSON><PERSON><PERSON><PERSON>", "number": "36", "city": "Moon city", "postcode": "245643", "father_name": "Manolis", "landline_telephone": "465352358", "mobile_telephone": "698713245", "email": "<EMAIL>"}, {"id": "98", "name": "La", "surname": "Polizia", "student_number": "10434090", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "number": "46", "city": "<PERSON><PERSON>", "postcode": "164542", "father_name": "Klav<PERSON>s", "landline_telephone": "4673596", "mobile_telephone": "55464852", "email": "<EMAIL>"}, {"id": "99", "name": "manousos", "surname": "Dlabiras", "student_number": "10434091", "street": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "number": "47", "city": "Tripoli", "postcode": "23100", "father_name": "<PERSON><PERSON>", "landline_telephone": "23242424", "mobile_telephone": "24242424", "email": "<EMAIL>"}, {"id": "100", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "student_number": "10434092", "street": "<PERSON><PERSON><PERSON>", "number": "28", "city": "Athens", "postcode": "10551", "father_name": "<PERSON><PERSON><PERSON>", "landline_telephone": "2105863247", "mobile_telephone": "6945218947", "email": "<EMAIL>"}, {"id": "101", "name": "<PERSON>", "surname": "<PERSON>", "student_number": "10434093", "street": "White House", "number": "911", "city": "Washington ", "postcode": "2049", "father_name": "<PERSON>", "landline_telephone": "2024561111", "mobile_telephone": "2024561414", "email": "<EMAIL>"}], "professors": [{"id": "1", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Network-centric systems", "landline": "2610996915", "mobile": "6977998877", "department": "CEID", "university": "University of Patras"}, {"id": "7", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Integrated Systems", "landline": "2610885511", "mobile": "6988812345", "department": "CEID", "university": "University of Patras"}, {"id": "8", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Artificial Intelligence", "landline": "23", "mobile": "545", "department": "CEID", "university": "University of Patras"}, {"id": "9", "name": "Eleni", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "WEB", "landline": "34", "mobile": "245", "department": "CEID", "university": "University of Patras"}, {"id": "10", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "email": "<EMAIL>", "topic": "Artificial Intelligence", "landline": "2610170390", "mobile": "6917031990", "department": "CEID", "university": "University of Patras"}, {"id": "11", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Data Engineering", "landline": "2610324365", "mobile": "6978530352", "department": "IT", "university": "University of Patras"}, {"id": "12", "name": "<PERSON><PERSON><PERSON>", "surname": "Kara<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "informatics", "landline": "2610324242", "mobile": "6934539920", "department": "CEID", "university": "University of Patras"}, {"id": "13", "name": "<PERSON><PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Arxeologia", "landline": "2610945934", "mobile": "6947845334", "department": "Arxeologias", "university": "UOI"}, {"id": "14", "name": "<PERSON><PERSON><PERSON>", "surname": "Makaveli", "email": "<EMAIL>", "topic": "Business", "landline": "2310231023", "mobile": "6929349285", "department": "Economics", "university": "UOA"}, {"id": "15", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "SQL injections", "landline": "1234567890", "mobile": "6988223322", "department": "Engineering", "university": "University of SKG"}, {"id": "16", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "t", "landline": "2610333999", "mobile": "6999990999", "department": "CEID", "university": "UoP"}, {"id": "17", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Big Data", "landline": "2264587412", "mobile": "6996116921", "department": "CEID", "university": "University of Patras"}, {"id": "18", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Pharmaceutical Drugs", "landline": "69", "mobile": "**********", "department": "Chemistry", "university": "University of Streets"}, {"id": "19", "name": "<PERSON><PERSON><PERSON>", "surname": "Restas", "email": "<EMAIL>", "topic": "Nekro8aftiki", "landline": "********", "mobile": "********", "department": "Nekro8aftikis", "university": "University Of Ohio"}, {"id": "20", "name": "Fat ", "surname": "Banker", "email": "<EMAIL>", "topic": "kippah", "landline": "**********", "mobile": "**********", "department": "Froutemporiki", "university": "University of Israel"}, {"id": "21", "name": "<PERSON><PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "topic": "Logistics", "landline": "**********", "mobile": "**********", "department": "Social Rehabitation", "university": "University of UAE"}, {"id": "22", "name": "Stefania", "surname": "<PERSON><PERSON>", "email": "<EMAIL>", "topic": "Information Theory", "landline": "**********", "mobile": "**********", "department": "ECE", "university": "University of Patras"}, {"id": "23", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Telecommunication Electronics", "landline": "**********", "mobile": "**********", "department": "ECE", "university": "University of Patras\t"}, {"id": "24", "name": "<PERSON><PERSON><PERSON><PERSON> ", "surname": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Physics", "landline": "210-1234567", "mobile": "690-1234567", "department": "Physics", "university": "National and Kapodistrian University of Athens"}, {"id": "25", "name": "<PERSON><PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "topic": "Statistics and Probability", "landline": "2310-7654321", "mobile": "694-7654321", "department": "Mathematics", "university": "Aristotle University of Thessaloniki"}, {"id": "26", "name": "<PERSON>", "surname": "<PERSON><PERSON>", "email": "<EMAIL>", "topic": "Artificial Intelligence", "landline": "2610-9876543", "mobile": "697-9876543", "department": "Computer Science", "university": "University of Patras"}, {"id": "27", "name": "Sophia", "surname": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Economic Theory", "landline": "2310-5432109", "mobile": "698-5432109", "department": "Economics", "university": "Athens University of Economics and Business"}, {"id": "28", "name": "<PERSON> ", "surname": "<PERSON><PERSON><PERSON>", "email": "micha<PERSON><PERSON><PERSON>@gmail.com", "topic": "Renewable Energy Systems", "landline": "2610-4455667", "mobile": "697-4455667", "department": "Electrical Engineering", "university": "University of Ioannina"}, {"id": "29", "name": "Elon", "surname": "Musk", "email": "<EMAIL>", "topic": "Electric Vehicles", "landline": "**************", "mobile": "<PERSON><PERSON>", "department": "Department of Physics", "university": "University of Pennsylvania, Philadelphia"}, {"id": "30", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "AI", "landline": "2610121212", "mobile": "6912121212", "department": "department", "university": "University"}, {"id": "32", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "topic", "landline": "land", "mobile": "mob", "department": "dep", "university": "university"}, {"id": "33", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "top", "landline": "la", "mobile": "mo", "department": "de", "university": "university"}, {"id": "34", "name": "patrick", "surname": "xrusopsaros", "email": "<EMAIL>", "topic": "thalasioi i<PERSON>i", "landline": "2610567917", "mobile": "6952852742", "department": "Solomos", "university": "Nemo"}, {"id": "35", "name": "<PERSON><PERSON><PERSON>", "surname": "koutsikos", "email": "<EMAIL>", "topic": "<PERSON><PERSON>a", "landline": "2298042035", "mobile": "6969696969", "department": "Ktinotrofia", "university": "University of Methana"}, {"id": "36", "name": "<PERSON>", "surname": "Auditore da Firenze", "email": "<EMAIL>", "topic": "assassinations", "landline": "null", "mobile": "null", "department": "<PERSON><PERSON><PERSON><PERSON>", "university": "University of Assasinos"}, {"id": "37", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Bet Predictions", "landline": "1235654899", "mobile": "2310521010", "department": "opap", "university": "London"}, {"id": "38", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON>", "email": "<EMAIL>", "topic": "Probability", "landline": "2610486396", "mobile": "698888884", "department": "Computer Engineering", "university": "University of Beegwean"}, {"id": "39", "name": "<PERSON>", "surname": "Mendilibar", "email": "<EMAIL>", "topic": "Sentres", "landline": "2105555555", "mobile": "6922222222", "department": "Conference League", "university": "Uni of Olympiacos"}, {"id": "40", "name": "<PERSON>", "surname": "<PERSON>", "email": "liam<PERSON><PERSON>@ceid.upatras.gr", "topic": "Cryptography", "landline": "2462311345", "mobile": "6980847234", "department": "CEID", "university": "University of Patras"}, {"id": "41", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "topic": "Oriented programing", "landline": "2310221234", "mobile": "6971006355", "department": "CEID", "university": "University of Patras"}, {"id": "42", "name": "<PERSON><PERSON> ", "surname": "<PERSON><PERSON>", "email": "<EMAIL>", "topic": "manas", "landline": "spiti", "mobile": "69854512", "department": "<PERSON><PERSON>", "university": "Hastle University"}, {"id": "43", "name": "Oikoumenikos", "surname": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "<PERSON><PERSON>", "landline": "987546123", "mobile": "69 6 9 69", "department": "<PERSON><PERSON><PERSON><PERSON>", "university": "Nation University Of Pakistan"}, {"id": "44", "name": "<PERSON><PERSON><PERSON>", "surname": "Snape", "email": "<EMAIL>", "topic": "math 2", "landline": "26210 26441", "mobile": "6926626226", "department": "ceid", "university": "University of Patras"}, {"id": "45", "name": "<PERSON><PERSON>", "surname": "Sahur", "email": "<EMAIL>", "topic": "Graphs", "landline": "210 1425735", "mobile": "69434619363", "department": "CEID", "university": "University of Patras"}, {"id": "46", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Computer science", "landline": "2610123456", "mobile": "6912345678", "department": "CEID", "university": "University of Patras"}, {"id": "47", "name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Physics", "landline": "2610111111", "mobile": "6911111111", "department": "CEID", "university": "University of Patras"}, {"id": "48", "name": "MARI", "surname": "BRO", "email": "<EMAIL>", "topic": "Life", "landline": "666", "mobile": "666", "department": "no", "university": "University of Brain"}, {"id": "49", "name": "<a href=\"https://www.youtube.com\">G</a>", "surname": "Goa<PERSON>", "email": "<EMAIL>", "topic": "no", "landline": "666", "mobile": "666", "department": "No", "university": "University of Goats"}, {"id": "50", "name": "Brain", "surname": "Rot", "email": "<EMAIL>", "topic": "no", "landline": "9", "mobile": "6", "department": "<PERSON><PERSON>", "university": "<a href=\"https://www.youtube.com/watch?v=nxSbhVnwdFw&t=1121s\">Crocodilo</a>"}, {"id": "51", "name": "<PERSON><PERSON><PERSON> ", "surname": "Sinsidis", "email": "joh<PERSON><PERSON>@upatras.gr", "topic": "Yps<PERSON><PERSON>rdiak<PERSON>i", "landline": "2610645698", "mobile": "697878787", "department": "<PERSON><PERSON><PERSON><PERSON><PERSON> kin<PERSON>", "university": "University of Makias"}, {"id": "52", "name": "<PERSON><PERSON><PERSON>", "surname": "Fragkofonias", "email": "<EMAIL>", "topic": "oikonomia tou tsipi", "landline": "2610546132", "mobile": "697878787", "department": "Real Economics ", "university": "University Of Empty Pocket"}, {"id": "53", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "<PERSON><PERSON><PERSON>ulos to fainomeno", "landline": "2610212121", "mobile": "6921212121", "department": "<PERSON><PERSON><PERSON>", "university": "University of the Road"}, {"id": "54", "name": "<PERSON><PERSON><PERSON><PERSON> ", "surname": "Frour<PERSON>", "email": "<EMAIL>", "topic": "al<PERSON><PERSON><PERSON> p<PERSON>la", "landline": "261056458", "mobile": "698778788", "department": "Panathinaiki agwgh", "university": "University of tears"}, {"id": "55", "name": "<PERSON> ", "surname": "Bonassera", "email": "<EMAIL>", "topic": "<PERSON><PERSON><PERSON> al<PERSON>e", "landline": "23131131", "mobile": "6575754", "department": "cuccina italiana", "university": "Carbonara University"}, {"id": "56", "name": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "topic": "Basketball Strategy", "landline": "2108743265", "mobile": "6932178542", "department": "SEF", "university": "University of Gate 7"}]}