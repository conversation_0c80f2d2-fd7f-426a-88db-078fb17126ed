{"name": "my-express-app", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www", "dev": "nodemon ./bin/www", "seed": "node ./db/seedDatabase.js"}, "dependencies": {"bcrypt": "^6.0.0", "cookie-parser": "~1.4.4", "debug": "~2.6.9", "dotenv": "^17.0.1", "ejs": "^3.1.10", "express": "^4.21.2", "express-mysql-session": "^3.0.3", "express-session": "^1.18.1", "formidable": "^3.5.4", "http-errors": "~1.6.3", "mariadb": "^3.4.4", "mongoose": "^8.16.1", "morgan": "~1.9.1"}, "devDependencies": {"nodemon": "^3.1.10"}}