var express = require("express");
var router = express.Router();
var path = require("path");
const checkPermission = require("../../middlewares/checkPermission");
var formidable = require('formidable');
const fs = require('fs');
const pool = require("../../db/db");

async function insertThesisToDB(req, res, fields, safeName) {
  // SQL query to insert thesis details into the database
  const sql = `
    INSERT INTO thesis 
    (supervisor_id, member1_id, member2_id,
    student_id, title, description,
    pdf, grade)
    
    VALUES 
    (?, NULL, NULL, 
    NULL, ?, ?,
    ?, NULL) 
    `;

  // Prepare query parameters
  params = [req.session.userId, fields.title, fields.summary, safeName]

  // Get a connection from the pool
  const conn = await pool.getConnection();
  try {
    // Execute the query
    const rows = await conn.query(sql, params);
    conn.release();
    res.status(200).send("Thesis submitted successfully")

    // Return the first row if found, otherwise null
    if (rows.length > 0) {
      return rows[0];
    } else {
      return null;
    }
  } catch (err) {
    if (err.code === 'ER_DUP_ENTRY') {
      console.error('Duplicate entry error:', err.message);
      // Handle gracefully (e.g., send user-friendly response)
      return res.status(409).send("Thesis already exists")

    }
    else {
      // Release the connection and propagate the error
      conn.release();
      throw err;
    }
  }
}

async function updateThesis(req, res, fields, safeName) {
  let sql = null;
  console.log(safeName)
  // SQL query to insert thesis details into the database
  if (safeName !== 'NULL') {
    sql = `
    UPDATE thesis SET
    title = ?,
    description = ?,
    pdf = ?
    WHERE id = ?
    `;
    params = [fields.title, fields.summary, safeName, fields.id]

  }
  else {
    sql = `
    UPDATE thesis SET
    title = ?,
    description = ?
    WHERE id = ?
    `;
    params = [fields.title, fields.summary, fields.id]

  }

  // Prepare query parameters

  // Log the SQL query and parameters for debugging
  console.log("Update Thesis SQL Query:", sql);
  console.log("Update Thesis Params:", params);


  // Get a connection from the pool
  const conn = await pool.getConnection();
  try {
    // Execute the query
    const rows = await conn.query(sql, params);
    conn.release();
    res.status(200).send("Thesis updated successfully")

    // Return the first row if found, otherwise null
    if (rows.length > 0) {
      return rows[0];
    } else {
      return null;
    }
  } catch (err) {
    if (err.code === 'ER_DUP_ENTRY') {
      console.error('Duplicate entry error:', err.message);
      // Handle gracefully (e.g., send user-friendly response)
      return res.status(409).send("Thesis already exists")
    }
    else {
      // Release the connection and propagate the error
      conn.release();
      throw err;
    }
  }
}

// Handles thesis submission, including file upload and database insertion
function submitThesis(req, res, action) {
  // Create a new formidable form for parsing file uploads
  const form = new formidable.IncomingForm();
  const fsPromises = fs.promises;

  // Define the directory to store uploaded files
  const uploadDir = path.join(process.cwd(), 'uploads');
  form.keepExtensions = true;

  // Parse the incoming request containing fields and files
  form.parse(req, async (err, fields, files) => {
    let safeName;
    // Get the uploaded PDF file (handle both array and single file cases)
    const file = Array.isArray(files.pdf) ? files.pdf[0] : files.pdf;
    if (file) {
      // Prepare paths for moving the uploaded file
      const oldPath = file.filepath;
      safeName = path.basename(file.originalFilename);
      const newPath = path.join(uploadDir, safeName);

      // Log thesis details for debugging
      console.log(fields.title);
      console.log(fields.summary);
      console.log(safeName);

      try {
        // Move the uploaded file to the uploads directory
        await fsPromises.copyFile(oldPath, newPath);
        await fsPromises.unlink(oldPath);
      } catch (error) {
        console.error(error);
        res.status(500).send('Error saving the file');
      }
    }
    else {
      safeName = 'NULL'
    }
    if (action === 'insert')
      insertThesisToDB(req, res, fields, safeName);
    else if (action === 'update')
      updateThesis(req, res, fields, safeName);

  });
}

// Function to fetch under assignment thesis' of current professor
async function getUnderAssignment(req) {
  // SQL query to select user and student fields by user ID
  const sql = `
    SELECT id, title, description, pdf
    FROM thesis
    WHERE supervisor_id = ? AND thesis_status = 'under-assignment'
  `;

  // Use the userId from the session as the query parameter
  const params = [req.session.userId];
  // Get a connection from the pool
  const conn = await pool.getConnection();
  try {
    // Execute the query
    const rows = await conn.query(sql, params);
    conn.release();

    // Return the first row if found, otherwise null
    if (rows.length > 0) {
      console.log(rows)
      return rows;
    } else {
      return null;
    }
  } catch (err) {
    // Release the connection and propagate the error
    conn.release();
    throw err;
  }
}


// Route: GET /professor/get-info
// Fetch and return the professors thesis' under assignment as JSON
router.get('/get-under-assignment', async (req, res) => {
  try {
    // Retrieve student information from the database
    const info = await getUnderAssignment(req);
    if (info) {
      // Set response header to JSON and send the info
      // Convert BigInt values to strings if present
      res.setHeader('Content-Type', 'application/json');
      res.send(JSON.stringify({ info }, (_, v) =>
        typeof v === 'bigint' ? v.toString() : v
      ));
    } else {
      // If no info found, send 401 error
      res.status(401).json({ error: 'Could not fetch Data' });
    }
  } catch (err) {
    // Log and send server error if something goes wrong
    console.error('Error in /get-info:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Route: POST /professor/create-topic/
// Saves a new thesis created by a professor
router.post('/create-topic', (req, res) => {
  submitThesis(req, res, 'insert');
});

router.post('/update-topic', (req, res) => {
  submitThesis(req, res, 'update');
});

// Route: GET /professor/
// Serve the main professor dashboard page
router.get("/", checkPermission('professor'), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/professor.html"));
});

// Route: GET /professor/create
// Serve the page for creating a new thesis
router.get("/create", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/create_thesis.html"));
});

// Route: GET /professor/assign
// Serve the page for assigning a thesis
router.get("/assign", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/assign_thesis.html"));
});

// Route: GET /professor/view_thesis
// Serve the page for viewing theses
router.get("/view_thesis", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/view_thesis.html"));
});

// Route: GET /professor/invitations
// Serve the page for viewing invitations
router.get("/invitations", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/invitations.html"));
});

// Route: GET /professor/stats
// Serve the statistics page for professors
router.get("/stats", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/statistics.html"));
});

// Route: GET /professor/manage
// Serve the page for managing professor-related data
router.get("/manage", checkPermission("professor"), (req, res) => {
  res.sendFile(path.join(__dirname, "../../public/professor/manage.html"));
});

// Route: GET /professor/search-students
// Search for students by student number or name
router.get('/search-students', async (req, res) => {
  try {
    const { query } = req.query;

    if (!query || query.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters long' });
    }

    const students = await searchStudents(query.trim());
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify({ students }, (_, v) =>
      typeof v === 'bigint' ? v.toString() : v
    ));
  } catch (err) {
    console.error('Error in /search-students:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Route: GET /professor/available-topics
// Get available thesis topics for assignment
router.get('/available-topics', async (req, res) => {
  try {
    const topics = await getAvailableTopics(req);
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify({ topics }, (_, v) =>
      typeof v === 'bigint' ? v.toString() : v
    ));
  } catch (err) {
    console.error('Error in /available-topics:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Route: GET /professor/temporary-assignments
// Get temporary assignments awaiting committee approval
router.get('/temporary-assignments', async (req, res) => {
  try {
    const assignments = await getTemporaryAssignments(req);
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify({ assignments }, (_, v) =>
      typeof v === 'bigint' ? v.toString() : v
    ));
  } catch (err) {
    console.error('Error in /temporary-assignments:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Route: POST /professor/assign-thesis
// Assign a thesis to a student
router.post('/assign-thesis', async (req, res) => {
  try {
    const { thesisId, studentId } = req.body;

    if (!thesisId || !studentId) {
      return res.status(400).json({ error: 'Thesis ID and Student ID are required' });
    }

    const result = await assignThesisToStudent(req, thesisId, studentId);

    if (result.success) {
      res.status(200).json({ message: 'Thesis assigned successfully' });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (err) {
    console.error('Error in /assign-thesis:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Route: POST /professor/cancel-assignment
// Cancel a thesis assignment by removing student_id
router.post('/cancel-assignment', async (req, res) => {
  try {
    const { thesisId } = req.body;

    if (!thesisId) {
      return res.status(400).json({ error: 'Thesis ID is required' });
    }

    const result = await cancelThesisAssignment(req, thesisId);

    if (result.success) {
      res.status(200).json({ message: 'Assignment cancelled successfully' });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (err) {
    console.error('Error in /cancel-assignment:', err);
    res.status(500).json({ error: 'Server Error' });
  }
});

// Function to search for students by student number or name
async function searchStudents(query) {
  const sql = `
    SELECT
      user.id,
      user.name,
      user.surname,
      user.email,
      student.student_number
    FROM user
    INNER JOIN student ON user.id = student.id
    WHERE
      student.student_number LIKE ? OR
      user.name LIKE ? OR
      user.surname LIKE ? OR
      CONCAT(user.name, ' ', user.surname) LIKE ?
    ORDER BY student.student_number
    LIMIT 10
  `;

  const searchPattern = `%${query}%`;
  const params = [searchPattern, searchPattern, searchPattern, searchPattern];

  const conn = await pool.getConnection();
  try {
    const rows = await conn.query(sql, params);
    conn.release();
    return rows || [];
  } catch (err) {
    conn.release();
    throw err;
  }
}

// Function to get available thesis topics for assignment
async function getAvailableTopics(req) {
  const sql = `
    SELECT
      id,
      title,
      description,
      pdf,
      submission_date
    FROM thesis
    WHERE supervisor_id = ? AND thesis_status = 'under-assignment' AND student_id IS NULL
    ORDER BY submission_date DESC
  `;

  const params = [req.session.userId];

  const conn = await pool.getConnection();
  try {
    const rows = await conn.query(sql, params);
    conn.release();
    return rows || [];
  } catch (err) {
    conn.release();
    throw err;
  }
}

// Function to get temporary assignments awaiting committee approval
async function getTemporaryAssignments(req) {
  const sql = `
    SELECT
      t.id,
      t.title,
      t.description,
      t.submission_date,
      u.name as student_name,
      u.surname as student_surname,
      s.student_number
    FROM thesis t
    INNER JOIN student s ON t.student_id = s.id
    INNER JOIN user u ON s.id = u.id
    WHERE t.supervisor_id = ? AND t.thesis_status = 'under-assignment' AND t.student_id IS NOT NULL
    ORDER BY t.submission_date DESC
  `;

  const params = [req.session.userId];

  const conn = await pool.getConnection();
  try {
    const rows = await conn.query(sql, params);
    conn.release();
    return rows || [];
  } catch (err) {
    conn.release();
    throw err;
  }
}

// Function to assign a thesis to a student
async function assignThesisToStudent(req, thesisId, studentId) {
  const conn = await pool.getConnection();

  try {
    // Start transaction
    await conn.beginTransaction();

    // First, verify that the thesis belongs to the current professor and is available
    const checkThesisSql = `
      SELECT id, title, student_id
      FROM thesis
      WHERE id = ? AND supervisor_id = ? AND thesis_status = 'under-assignment'
    `;
    const thesisRows = await conn.query(checkThesisSql, [thesisId, req.session.userId]);

    if (thesisRows.length === 0) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Thesis not found or not available for assignment' };
    }

    // Check if thesis is already assigned
    if (thesisRows[0].student_id !== null) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Thesis is already assigned to another student' };
    }

    // Verify that the student exists
    const checkStudentSql = `
      SELECT id FROM student WHERE id = ?
    `;
    const studentRows = await conn.query(checkStudentSql, [studentId]);

    if (studentRows.length === 0) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Student not found' };
    }

    // Update the thesis with the student ID
    const updateSql = `
      UPDATE thesis
      SET student_id = ?
      WHERE id = ? AND supervisor_id = ?
    `;
    const updateResult = await conn.query(updateSql, [studentId, thesisId, req.session.userId]);

    if (updateResult.affectedRows === 0) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Failed to assign thesis' };
    }

    // Commit transaction
    await conn.commit();
    conn.release();

    return { success: true };

  } catch (err) {
    await conn.rollback();
    conn.release();
    console.error('Error in assignThesisToStudent:', err);
    throw err;
  }
}

// Function to cancel a thesis assignment
async function cancelThesisAssignment(req, thesisId) {
  const conn = await pool.getConnection();

  try {
    // Start transaction
    await conn.beginTransaction();

    // First, verify that the thesis belongs to the current professor and is assigned
    const checkThesisSql = `
      SELECT id, title, student_id
      FROM thesis
      WHERE id = ? AND supervisor_id = ? AND thesis_status = 'under-assignment'
    `;
    const thesisRows = await conn.query(checkThesisSql, [thesisId, req.session.userId]);

    if (thesisRows.length === 0) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Thesis not found or not available for cancellation' };
    }

    // Check if thesis is actually assigned to a student
    if (thesisRows[0].student_id === null) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Thesis is not currently assigned to any student' };
    }

    // Remove the student assignment by setting student_id to NULL
    const updateSql = `
      UPDATE thesis
      SET student_id = NULL
      WHERE id = ? AND supervisor_id = ?
    `;
    const updateResult = await conn.query(updateSql, [thesisId, req.session.userId]);

    if (updateResult.affectedRows === 0) {
      await conn.rollback();
      conn.release();
      return { success: false, error: 'Failed to cancel assignment' };
    }

    // Commit transaction
    await conn.commit();
    conn.release();

    return { success: true };

  } catch (err) {
    await conn.rollback();
    conn.release();
    console.error('Error in cancelThesisAssignment:', err);
    throw err;
  }
}

// Export the router to be used in the main app
module.exports = router;
