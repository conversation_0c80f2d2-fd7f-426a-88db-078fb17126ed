/* Primary color */
:root {
    --custom-primary: #00274e;
    --custom-primary-subtle: #a0d0ff;
    --custom-primary-hover: #014b96;
    /* darker shade for hover */
    --custom-background: #014b9604;
}

/* Button primary */
.btn-primary {
    background-color: var(--custom-primary) !important;
    border-color: var(--custom-primary) !important;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.show>.btn-primary.dropdown-toggle {
    background-color: var(--custom-primary-hover) !important;
    border-color: var(--custom-primary-hover) !important;
}

/* Background primary utility */
body .bg-primary {
    background-color: var(--custom-primary) !important;
    border-color: var(--custom-primary) !important;
}

body .bg-primary-subtle {
    background-color: var(--custom-background) !important;
}

body {
    visibility: hidden;
    background-color: var(--custom-background) !important;
    /* hide the entire page (to avoid flashing after partial loading)*/
}

.extra-small-text {
    font-size: 12px;
}

footer {
    width: 100%;
    /* Make the footer stretch to full width */
    position: relative;
    bottom: 0;
    left: 0;
    /* Stretch to the left edge */
    text-align: center;
    /* Center the text */
}

/* Make the second bar sticky at the top */
.white-bar {
    background-color: #ffffff;
    position: sticky;
    z-index: 10;
    top: 0;
    /* Make sure it's above other content */
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    /* Light shadow */
    transform-origin: top;
    transition: transform 0.25s ease;
}

.video-container video {
    position: absolute;
    width: 100%;
    transform: translateY(-55%);
    height: 100%;
    max-height: 69vh;

    object-fit: cover;
    z-index: -1;
    /* Push video behind content */
}

.floating-bar {
    width: 100%;
    max-width: 1200px;
    position: relative;
    transform: translateY(20vh);
}

.shadow-left-right {
    box-shadow:
        7px 0 0 rgba(0, 0, 0, 0.05),
        -7px 0 0 rgba(0, 0, 0, 0.05);
}

label {
    display: block;
    font:
        1rem "Fira Sans",
        sans-serif;
}

input,
label {
    margin: 0.4rem 0;
}

table,
tr,
th,
td {
    border: 1px solid #dddddd;
    border-collapse: collapse;
}

.tbl-header {
    width: calc(100% - 17px);
    width: -webkit-calc(100% - 17px);
    width: -moz-calc(100% - 17px);
}

.card {
    border-radius: 4px;
    box-shadow: 0 6px 10px rgba(0, 0, 0, .08), 0 0 6px rgba(0, 0, 0, .05);
    transition: .5s transform cubic-bezier(.155, 1.105, .295, 1.12), .3s box-shadow, .3s -webkit-transform cubic-bezier(.155, 1.105, .295, 1.12);
    padding: 14px 80px 18px 36px;
    cursor: pointer;
}

.card:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}